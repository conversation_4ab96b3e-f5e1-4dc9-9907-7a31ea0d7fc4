<script setup lang="ts">
import DecoupledEditor from '@ckeditor/ckeditor5-build-decoupled-document';
import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import { ref, watch } from 'vue';
import { v4 as uuid } from 'uuid';

const props = defineProps({
  id: {
    type: String,
    default() {
      return `ckeditor-${uuid()}`;
    },
  },
  modelValue: {
    type: String,
    default: '',
  },
  error: String,
  label: String,
  disabled: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const editor = DecoupledEditor;
const editorData = ref(props.modelValue);

// Watch for external changes to modelValue
watch(
  () => props.modelValue,
  newValue => {
    if (newValue !== editorData.value) {
      editorData.value = newValue;
    }
  },
);

// Watch for internal changes to editorData
watch(editorData, newValue => {
  emit('update:modelValue', newValue);
});

const editorConfig = {
  placeholder: props.placeholder,
  toolbar: [
    'heading',
    '|',
    'bold',
    'italic',
    'link',
    'bulletedList',
    'numberedList',
    '|',
    'outdent',
    'indent',
    '|',
    'blockQuote',
    'insertTable',
    'undo',
    'redo',
  ],
  // Remove upload functionality for now - can be added later if needed
  // ckfinder: {
  //   uploadUrl: '/upload'
  // }
};

const onReady = (editor: any) => {
  // Insert the toolbar before the editable area
  const toolbarContainer = document.querySelector(`#${props.id}-toolbar`);
  if (toolbarContainer) {
    toolbarContainer.appendChild(editor.ui.view.toolbar.element);
  }

  // Handle disabled state
  if (props.disabled) {
    editor.enableReadOnlyMode('disabled');
  }
};
</script>

<template>
  <div class="space-y-6" :class="$attrs.class">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <div class="ckeditor-container">
      <div
        :id="`${id}-toolbar`"
        class="ck-toolbar-container"
        :class="[error ? 'error-toolbar' : 'normal-toolbar', disabled ? 'bg-gray-100' : 'bg-white']"
      ></div>

      <div
        class="ck-editor-wrapper"
        :class="[error ? 'error-input' : 'normal-input', disabled ? 'bg-gray-100' : 'bg-white']"
      >
        <Ckeditor
          :id="id"
          :editor="editor"
          v-model="editorData"
          :config="editorConfig"
          @ready="onReady"
          :disabled="disabled"
        />
      </div>
    </div>

    <div v-if="error" class="mt-1 text-theme-sm text-error-500">{{ error }}</div>
  </div>
</template>

<style scoped>
.ckeditor-container {
  border-radius: 0.5rem;
  overflow: hidden;
}

.ck-toolbar-container {
  border: 1px solid #d1d5db;
  border-bottom: none;
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}

.ck-editor-wrapper {
  border: 1px solid #d1d5db;
  border-top: none;
  border-bottom-left-radius: 0.5rem;
  border-bottom-right-radius: 0.5rem;
  min-height: 200px;
}

/* Normal state */
.normal-toolbar {
  border-color: #d1d5db;
}

.normal-input {
  border-color: #d1d5db;
}

.normal-input:focus-within {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Error state */
.error-toolbar {
  border-color: #ef4444;
}

.error-input {
  border-color: #ef4444;
}

.error-input:focus-within {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* CKEditor specific styles */
:deep(.ck-editor__editable) {
  border: none !important;
  border-radius: 0 !important;
  box-shadow: none !important;
  padding: 1rem !important;
  min-height: 150px !important;
}

:deep(.ck-toolbar) {
  border: none !important;
  border-radius: 0 !important;
  background: transparent !important;
  padding: 0.75rem 1rem !important;
}

:deep(.ck-toolbar__separator) {
  background: #d1d5db !important;
}

/* Disabled state */
.bg-gray-100 :deep(.ck-editor__editable) {
  background-color: #f3f4f6 !important;
  color: #6b7280 !important;
}

.bg-gray-100 :deep(.ck-toolbar) {
  background-color: #f3f4f6 !important;
}
</style>
