<script setup lang="ts">
import { ref, onMounted, watch, nextTick } from 'vue';
import { v4 as uuid } from 'uuid';
import { useRichTextEditor } from '@/composables/useRichTextEditor';

const props = defineProps({
  id: {
    type: String,
    default() {
      return `rich-editor-${uuid()}`;
    },
  },
  modelValue: {
    type: String,
    default: '',
  },
  error: String,
  label: String,
  disabled: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: 'Nhập nội dung...',
  },
  height: {
    type: String,
    default: '300px',
  },
});

const emit = defineEmits(['update:modelValue']);

const editorRef = ref<HTMLElement>();
const isFullscreen = ref(false);

const {
  execCommand,
  queryCommandState,
  queryCommandValue,
  insertHTML,
  createLink,
  insertImage,
  toggleFullscreen,
  undo,
  redo,
  canUndo,
  canRedo,
  getCurrentSelection,
  restoreSelection,
} = useRichTextEditor(editorRef);

// Font families
const fontFamilies = [
  { name: 'Arial', value: 'Arial, sans-serif' },
  { name: 'Times New Roman', value: 'Times New Roman, serif' },
  { name: 'Courier New', value: 'Courier New, monospace' },
  { name: 'Helvetica', value: 'Helvetica, sans-serif' },
  { name: 'Georgia', value: 'Georgia, serif' },
];

// Font sizes
const fontSizes = [
  { name: '8px', value: '1' },
  { name: '10px', value: '2' },
  { name: '12px', value: '3' },
  { name: '14px', value: '4' },
  { name: '18px', value: '5' },
  { name: '24px', value: '6' },
  { name: '36px', value: '7' },
];

// Colors
const colors = [
  '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
  '#800000', '#008000', '#000080', '#808000', '#800080', '#008080', '#C0C0C0',
  '#808080', '#9999FF', '#993366', '#FFFFCC', '#CCFFFF', '#660066', '#FF8080',
  '#0066CC', '#CCCCFF', '#000080', '#FF00FF', '#FFFF00', '#00FFFF', '#800080',
];

const onInput = () => {
  if (editorRef.value) {
    emit('update:modelValue', editorRef.value.innerHTML);
  }
};

const setContent = (content: string) => {
  if (editorRef.value && editorRef.value.innerHTML !== content) {
    editorRef.value.innerHTML = content;
  }
};

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  setContent(newValue);
});

// Initialize content
onMounted(() => {
  if (editorRef.value) {
    setContent(props.modelValue);
    
    // Set disabled state
    if (props.disabled) {
      editorRef.value.contentEditable = 'false';
    }
  }
});

// Toolbar actions
const handleBold = () => execCommand('bold');
const handleItalic = () => execCommand('italic');
const handleUnderline = () => execCommand('underline');
const handleStrikethrough = () => execCommand('strikethrough');
const handleSuperscript = () => execCommand('superscript');
const handleSubscript = () => execCommand('subscript');

const handleFontFamily = (fontFamily: string) => {
  execCommand('fontName', fontFamily);
};

const handleFontSize = (size: string) => {
  execCommand('fontSize', size);
};

const handleTextColor = (color: string) => {
  execCommand('foreColor', color);
};

const handleBackgroundColor = (color: string) => {
  execCommand('backColor', color);
};

const handleAlignment = (align: string) => {
  execCommand(`justify${align}`);
};

const handleList = (type: 'ordered' | 'unordered') => {
  if (type === 'ordered') {
    execCommand('insertOrderedList');
  } else {
    execCommand('insertUnorderedList');
  }
};

const handleIndent = (type: 'indent' | 'outdent') => {
  execCommand(type);
};

const handleLink = () => {
  const url = prompt('Nhập URL:');
  if (url) {
    createLink(url);
  }
};

const handleImage = () => {
  const url = prompt('Nhập URL hình ảnh:');
  if (url) {
    insertImage(url);
  }
};

const handleTable = () => {
  const rows = prompt('Số hàng:', '3');
  const cols = prompt('Số cột:', '3');
  
  if (rows && cols) {
    let tableHTML = '<table border="1" style="border-collapse: collapse; width: 100%;">';
    for (let i = 0; i < parseInt(rows); i++) {
      tableHTML += '<tr>';
      for (let j = 0; j < parseInt(cols); j++) {
        tableHTML += '<td style="padding: 8px; border: 1px solid #ccc;">&nbsp;</td>';
      }
      tableHTML += '</tr>';
    }
    tableHTML += '</table>';
    insertHTML(tableHTML);
  }
};

const handleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  toggleFullscreen(isFullscreen.value);
};

// Check if commands are active
const isBold = () => queryCommandState('bold');
const isItalic = () => queryCommandState('italic');
const isUnderline = () => queryCommandState('underline');
const isStrikethrough = () => queryCommandState('strikethrough');
</script>

<template>
  <div class="rich-text-editor" :class="{ 'fullscreen': isFullscreen, [$attrs.class]: true }">
    <label v-if="label" class="mb-1.5 block text-sm font-medium text-gray-700" :for="id">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <!-- Toolbar -->
    <div class="toolbar" :class="[error ? 'error-toolbar' : 'normal-toolbar', disabled ? 'bg-gray-100' : 'bg-white']">
      <!-- Font Family -->
      <select @change="handleFontFamily($event.target.value)" class="toolbar-select">
        <option value="">Font</option>
        <option v-for="font in fontFamilies" :key="font.value" :value="font.value">
          {{ font.name }}
        </option>
      </select>

      <!-- Font Size -->
      <select @change="handleFontSize($event.target.value)" class="toolbar-select">
        <option value="">Size</option>
        <option v-for="size in fontSizes" :key="size.value" :value="size.value">
          {{ size.name }}
        </option>
      </select>

      <div class="toolbar-separator"></div>

      <!-- Basic Formatting -->
      <button @click="handleBold" :class="{ active: isBold() }" class="toolbar-btn" title="Bold">
        <strong>B</strong>
      </button>
      <button @click="handleItalic" :class="{ active: isItalic() }" class="toolbar-btn" title="Italic">
        <em>I</em>
      </button>
      <button @click="handleUnderline" :class="{ active: isUnderline() }" class="toolbar-btn" title="Underline">
        <u>U</u>
      </button>
      <button @click="handleStrikethrough" :class="{ active: isStrikethrough() }" class="toolbar-btn" title="Strikethrough">
        <s>S</s>
      </button>

      <div class="toolbar-separator"></div>

      <!-- Text Color -->
      <div class="color-picker-group">
        <label class="toolbar-btn color-btn" title="Text Color">
          A
          <input type="color" @change="handleTextColor($event.target.value)" class="color-input">
        </label>
        <label class="toolbar-btn color-btn" title="Background Color">
          ■
          <input type="color" @change="handleBackgroundColor($event.target.value)" class="color-input">
        </label>
      </div>

      <div class="toolbar-separator"></div>

      <!-- Alignment -->
      <button @click="handleAlignment('Left')" class="toolbar-btn" title="Align Left">⬅</button>
      <button @click="handleAlignment('Center')" class="toolbar-btn" title="Align Center">↔</button>
      <button @click="handleAlignment('Right')" class="toolbar-btn" title="Align Right">➡</button>
      <button @click="handleAlignment('Full')" class="toolbar-btn" title="Justify">⬌</button>

      <div class="toolbar-separator"></div>

      <!-- Lists -->
      <button @click="handleList('unordered')" class="toolbar-btn" title="Bullet List">•</button>
      <button @click="handleList('ordered')" class="toolbar-btn" title="Numbered List">1.</button>

      <!-- Indent -->
      <button @click="handleIndent('outdent')" class="toolbar-btn" title="Decrease Indent">⬅</button>
      <button @click="handleIndent('indent')" class="toolbar-btn" title="Increase Indent">➡</button>

      <div class="toolbar-separator"></div>

      <!-- Insert -->
      <button @click="handleLink" class="toolbar-btn" title="Insert Link">🔗</button>
      <button @click="handleImage" class="toolbar-btn" title="Insert Image">🖼</button>
      <button @click="handleTable" class="toolbar-btn" title="Insert Table">⊞</button>

      <div class="toolbar-separator"></div>

      <!-- Undo/Redo -->
      <button @click="undo" :disabled="!canUndo" class="toolbar-btn" title="Undo">↶</button>
      <button @click="redo" :disabled="!canRedo" class="toolbar-btn" title="Redo">↷</button>

      <div class="toolbar-separator"></div>

      <!-- Fullscreen -->
      <button @click="handleFullscreen" class="toolbar-btn" title="Fullscreen">⛶</button>
    </div>

    <!-- Editor Content -->
    <div
      ref="editorRef"
      :id="id"
      class="editor-content"
      :class="[error ? 'error-input' : 'normal-input', disabled ? 'bg-gray-100' : 'bg-white']"
      :style="{ minHeight: height }"
      contenteditable="true"
      @input="onInput"
      :data-placeholder="placeholder"
    ></div>

    <div v-if="error" class="mt-1 text-sm text-red-500">{{ error }}</div>
  </div>
</template>
