import { ref, Ref } from 'vue';

export function useRichTextEditor(editorRef: Ref<HTMLElement | undefined>) {
  const undoStack = ref<string[]>([]);
  const redoStack = ref<string[]>([]);
  const canUndo = ref(false);
  const canRedo = ref(false);
  const currentSelection = ref<Range | null>(null);

  // Save current state for undo/redo
  const saveState = () => {
    if (editorRef.value) {
      const content = editorRef.value.innerHTML;
      undoStack.value.push(content);
      redoStack.value = []; // Clear redo stack when new action is performed
      canUndo.value = undoStack.value.length > 0;
      canRedo.value = false;
      
      // Limit undo stack size
      if (undoStack.value.length > 50) {
        undoStack.value.shift();
      }
    }
  };

  // Execute document command
  const execCommand = (command: string, value?: string) => {
    if (!editorRef.value) return;
    
    saveState();
    editorRef.value.focus();
    
    try {
      document.execCommand(command, false, value);
    } catch (error) {
      console.warn(`Command ${command} not supported:`, error);
    }
    
    // Trigger input event to update model
    editorRef.value.dispatchEvent(new Event('input', { bubbles: true }));
  };

  // Query command state
  const queryCommandState = (command: string): boolean => {
    try {
      return document.queryCommandState(command);
    } catch (error) {
      console.warn(`Query command state ${command} not supported:`, error);
      return false;
    }
  };

  // Query command value
  const queryCommandValue = (command: string): string => {
    try {
      return document.queryCommandValue(command);
    } catch (error) {
      console.warn(`Query command value ${command} not supported:`, error);
      return '';
    }
  };

  // Insert HTML at cursor position
  const insertHTML = (html: string) => {
    if (!editorRef.value) return;
    
    saveState();
    editorRef.value.focus();
    
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      range.deleteContents();
      
      const div = document.createElement('div');
      div.innerHTML = html;
      
      const fragment = document.createDocumentFragment();
      let node;
      while ((node = div.firstChild)) {
        fragment.appendChild(node);
      }
      
      range.insertNode(fragment);
      
      // Move cursor to end of inserted content
      range.collapse(false);
      selection.removeAllRanges();
      selection.addRange(range);
    }
    
    editorRef.value.dispatchEvent(new Event('input', { bubbles: true }));
  };

  // Create link
  const createLink = (url: string) => {
    if (!editorRef.value) return;
    
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      execCommand('createLink', url);
    } else {
      insertHTML(`<a href="${url}" target="_blank">${url}</a>`);
    }
  };

  // Insert image
  const insertImage = (url: string, alt: string = '') => {
    insertHTML(`<img src="${url}" alt="${alt}" style="max-width: 100%; height: auto;">`);
  };

  // Get current selection
  const getCurrentSelection = (): Range | null => {
    const selection = window.getSelection();
    if (selection && selection.rangeCount > 0) {
      return selection.getRangeAt(0).cloneRange();
    }
    return null;
  };

  // Restore selection
  const restoreSelection = (range: Range | null) => {
    if (range) {
      const selection = window.getSelection();
      if (selection) {
        selection.removeAllRanges();
        selection.addRange(range);
      }
    }
  };

  // Save selection
  const saveSelection = () => {
    currentSelection.value = getCurrentSelection();
  };

  // Undo
  const undo = () => {
    if (undoStack.value.length > 0 && editorRef.value) {
      const currentContent = editorRef.value.innerHTML;
      redoStack.value.push(currentContent);
      
      const previousContent = undoStack.value.pop();
      if (previousContent !== undefined) {
        editorRef.value.innerHTML = previousContent;
        editorRef.value.dispatchEvent(new Event('input', { bubbles: true }));
      }
      
      canUndo.value = undoStack.value.length > 0;
      canRedo.value = redoStack.value.length > 0;
    }
  };

  // Redo
  const redo = () => {
    if (redoStack.value.length > 0 && editorRef.value) {
      const currentContent = editorRef.value.innerHTML;
      undoStack.value.push(currentContent);
      
      const nextContent = redoStack.value.pop();
      if (nextContent !== undefined) {
        editorRef.value.innerHTML = nextContent;
        editorRef.value.dispatchEvent(new Event('input', { bubbles: true }));
      }
      
      canUndo.value = undoStack.value.length > 0;
      canRedo.value = redoStack.value.length > 0;
    }
  };

  // Toggle fullscreen
  const toggleFullscreen = (isFullscreen: boolean) => {
    if (editorRef.value) {
      const editor = editorRef.value.closest('.rich-text-editor') as HTMLElement;
      if (editor) {
        if (isFullscreen) {
          editor.style.position = 'fixed';
          editor.style.top = '0';
          editor.style.left = '0';
          editor.style.width = '100vw';
          editor.style.height = '100vh';
          editor.style.zIndex = '9999';
          editor.style.backgroundColor = 'white';
        } else {
          editor.style.position = '';
          editor.style.top = '';
          editor.style.left = '';
          editor.style.width = '';
          editor.style.height = '';
          editor.style.zIndex = '';
          editor.style.backgroundColor = '';
        }
      }
    }
  };

  // Format text with custom styles
  const formatText = (tag: string, styles?: Record<string, string>) => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      const range = selection.getRangeAt(0);
      const selectedText = range.toString();
      
      let element;
      if (tag === 'span') {
        element = document.createElement('span');
        if (styles) {
          Object.assign(element.style, styles);
        }
      } else {
        element = document.createElement(tag);
      }
      
      element.textContent = selectedText;
      range.deleteContents();
      range.insertNode(element);
      
      // Clear selection
      selection.removeAllRanges();
      
      editorRef.value?.dispatchEvent(new Event('input', { bubbles: true }));
    }
  };

  // Clean HTML (remove unwanted tags/attributes)
  const cleanHTML = (html: string): string => {
    const div = document.createElement('div');
    div.innerHTML = html;
    
    // Remove script tags
    const scripts = div.querySelectorAll('script');
    scripts.forEach(script => script.remove());
    
    // Remove on* attributes
    const allElements = div.querySelectorAll('*');
    allElements.forEach(element => {
      const attributes = Array.from(element.attributes);
      attributes.forEach(attr => {
        if (attr.name.startsWith('on')) {
          element.removeAttribute(attr.name);
        }
      });
    });
    
    return div.innerHTML;
  };

  // Get plain text content
  const getPlainText = (): string => {
    return editorRef.value?.textContent || '';
  };

  // Get word count
  const getWordCount = (): number => {
    const text = getPlainText().trim();
    return text ? text.split(/\s+/).length : 0;
  };

  // Get character count
  const getCharacterCount = (): number => {
    return getPlainText().length;
  };

  return {
    execCommand,
    queryCommandState,
    queryCommandValue,
    insertHTML,
    createLink,
    insertImage,
    getCurrentSelection,
    restoreSelection,
    saveSelection,
    undo,
    redo,
    canUndo,
    canRedo,
    toggleFullscreen,
    formatText,
    cleanHTML,
    getPlainText,
    getWordCount,
    getCharacterCount,
  };
}
