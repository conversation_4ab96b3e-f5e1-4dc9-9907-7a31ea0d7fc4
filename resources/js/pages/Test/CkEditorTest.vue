<script setup lang="ts">
import { ref } from 'vue';
import { Head } from '@inertiajs/vue3';
import CkEditor from '@/components/common/shared/CkEditor.vue';
import Button from '@/components/common/shared/Button.vue';

const content = ref('<p>This is a test content for CkEditor</p>');
const hasError = ref(false);
const isDisabled = ref(false);

const toggleError = () => {
  hasError.value = !hasError.value;
};

const toggleDisabled = () => {
  isDisabled.value = !isDisabled.value;
};

const clearContent = () => {
  content.value = '';
};
</script>

<template>
  <Head title="CkEditor Test" />
  
  <div class="p-6">
    <h1 class="text-2xl font-bold mb-6">CkEditor Component Test</h1>
    
    <!-- Control buttons -->
    <div class="mb-6 space-x-4">
      <Button @click="toggleError" variant="outline">
        {{ hasError ? 'Remove Error' : 'Add Error' }}
      </Button>
      <Button @click="toggleDisabled" variant="outline">
        {{ isDisabled ? 'Enable' : 'Disable' }}
      </Button>
      <Button @click="clearContent" variant="outline">
        Clear Content
      </Button>
    </div>
    
    <!-- CkEditor with all features -->
    <div class="max-w-4xl">
      <CkEditor
        v-model="content"
        label="Article Content"
        placeholder="Enter your article content here..."
        :error="hasError ? 'This field has an error' : ''"
        :disabled="isDisabled"
        :required="true"
        class="mb-6"
      />
    </div>
    
    <!-- Display current content -->
    <div class="mt-8">
      <h3 class="text-lg font-semibold mb-4">Current Content:</h3>
      <div class="p-4 bg-gray-100 rounded-lg">
        <pre class="whitespace-pre-wrap">{{ content }}</pre>
      </div>
    </div>
    
    <!-- Rendered HTML -->
    <div class="mt-8">
      <h3 class="text-lg font-semibold mb-4">Rendered HTML:</h3>
      <div class="p-4 border rounded-lg" v-html="content"></div>
    </div>
  </div>
</template>
