<script setup lang="ts">
import { ref } from 'vue';
import RichTextEditor from '../../components/common/shared/RichTextEditor.vue';

const content = ref('<p>Đ<PERSON>y là nội dung mẫu với <strong>text đậm</strong> và <em>text nghiêng</em>.</p><p>Bạn có thể chỉnh sửa nội dung này!</p>');
const content2 = ref('');
const hasError = ref(false);

const toggleError = () => {
  hasError.value = !hasError.value;
};

const clearContent = () => {
  content.value = '';
  content2.value = '';
};

const setSampleContent = () => {
  content.value = `
    <h1>Tiêu đề chính</h1>
    <h2>Tiêu đề phụ</h2>
    <p>Đ<PERSON><PERSON> là một đoạn văn bản thông thường với <strong>text đậm</strong>, <em>text nghiêng</em>, và <u>text gạch chân</u>.</p>
    
    <h3><PERSON>h sách không thứ tự:</h3>
    <ul>
      <li>Mục thứ nhất</li>
      <li>Mục thứ hai</li>
      <li>Mục thứ ba</li>
    </ul>
    
    <h3>Danh sách có thứ tự:</h3>
    <ol>
      <li>Bước đầu tiên</li>
      <li>Bước thứ hai</li>
      <li>Bước cuối cùng</li>
    </ol>
    
    <blockquote>
      Đây là một đoạn trích dẫn để minh họa tính năng blockquote.
    </blockquote>
    
    <p>Đây là một <a href="https://example.com" target="_blank">liên kết</a> trong văn bản.</p>
    
    <table border="1" style="border-collapse: collapse; width: 100%;">
      <tr>
        <th style="padding: 8px; border: 1px solid #ccc;">Cột 1</th>
        <th style="padding: 8px; border: 1px solid #ccc;">Cột 2</th>
        <th style="padding: 8px; border: 1px solid #ccc;">Cột 3</th>
      </tr>
      <tr>
        <td style="padding: 8px; border: 1px solid #ccc;">Dữ liệu 1</td>
        <td style="padding: 8px; border: 1px solid #ccc;">Dữ liệu 2</td>
        <td style="padding: 8px; border: 1px solid #ccc;">Dữ liệu 3</td>
      </tr>
    </table>
  `;
};
</script>

<template>
  <div class="p-8 max-w-6xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">Rich Text Editor Demo</h1>
    
    <!-- Controls -->
    <div class="mb-6 space-x-4">
      <button @click="toggleError" class="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600">
        {{ hasError ? 'Remove Error' : 'Show Error' }}
      </button>
      <button @click="clearContent" class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600">
        Clear Content
      </button>
      <button @click="setSampleContent" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
        Set Sample Content
      </button>
    </div>

    <!-- Editor 1 -->
    <div class="mb-8">
      <RichTextEditor
        v-model="content"
        label="Rich Text Editor với nội dung mẫu"
        :error="hasError ? 'Đây là thông báo lỗi mẫu' : ''"
        placeholder="Nhập nội dung của bạn tại đây..."
        height="400px"
        required
        class="mb-4"
      />
      
      <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">HTML Output:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40">{{ content }}</pre>
      </div>
    </div>

    <!-- Editor 2 -->
    <div class="mb-8">
      <RichTextEditor
        v-model="content2"
        label="Rich Text Editor trống"
        placeholder="Editor thứ hai để test..."
        height="250px"
        class="mb-4"
      />
      
      <div class="mt-4">
        <h3 class="text-lg font-semibold mb-2">HTML Output:</h3>
        <pre class="bg-gray-100 p-4 rounded text-sm overflow-auto max-h-40">{{ content2 || '(empty)' }}</pre>
      </div>
    </div>

    <!-- Disabled Editor -->
    <div class="mb-8">
      <RichTextEditor
        v-model="content"
        label="Rich Text Editor bị vô hiệu hóa"
        disabled
        height="200px"
        class="mb-4"
      />
    </div>

    <!-- Features List -->
    <div class="mt-12">
      <h2 class="text-2xl font-bold mb-4">Tính năng được hỗ trợ:</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <h3 class="text-lg font-semibold mb-2">Định dạng văn bản:</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Bold, Italic, Underline, Strikethrough</li>
            <li>Superscript, Subscript</li>
            <li>Font family và font size</li>
            <li>Text color và background color</li>
            <li>Text alignment (left, center, right, justify)</li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-2">Cấu trúc nội dung:</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Headings (H1, H2, H3, etc.)</li>
            <li>Bullet lists và numbered lists</li>
            <li>Indent và outdent</li>
            <li>Blockquotes</li>
            <li>Tables</li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-2">Media và liên kết:</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Insert links</li>
            <li>Insert images</li>
            <li>Insert tables</li>
          </ul>
        </div>
        
        <div>
          <h3 class="text-lg font-semibold mb-2">Tính năng khác:</h3>
          <ul class="list-disc list-inside space-y-1 text-sm">
            <li>Undo/Redo</li>
            <li>Fullscreen mode</li>
            <li>Responsive design</li>
            <li>Error state support</li>
            <li>Disabled state support</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style>
