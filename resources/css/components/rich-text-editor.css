/* Rich Text Editor Component Styles */

/* Main Container */
.rich-text-editor {
  @apply border border-gray-300 rounded-lg overflow-hidden;
}

.rich-text-editor.fullscreen {
  @apply fixed inset-0 z-50 bg-white;
}

/* Toolbar Styles */
.rich-text-editor .toolbar {
  @apply flex items-center gap-1 p-2 border-b border-gray-300 flex-wrap;
  background: linear-gradient(to bottom, #fafafa 0%, #f0f0f0 100%);
}

.rich-text-editor .normal-toolbar {
  @apply border-gray-300;
}

.rich-text-editor .error-toolbar {
  @apply border-red-500;
}

/* Toolbar Buttons */
.rich-text-editor .toolbar-btn {
  @apply px-2 py-1 text-sm border border-gray-300 rounded hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 min-w-[32px] h-8 flex items-center justify-center;
  transition: all 0.2s ease-in-out;
  background: linear-gradient(to bottom, #ffffff 0%, #f8f8f8 100%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rich-text-editor .toolbar-btn:hover {
  @apply bg-gray-50;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.rich-text-editor .toolbar-btn:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rich-text-editor .toolbar-btn:disabled {
  @apply opacity-50 cursor-not-allowed;
  transform: none;
  box-shadow: none;
}

.rich-text-editor .toolbar-btn.active {
  @apply bg-blue-100 border-blue-500 text-blue-700;
  background: linear-gradient(to bottom, #dbeafe 0%, #bfdbfe 100%);
  box-shadow: inset 0 1px 3px rgba(59, 130, 246, 0.2);
}

/* Toolbar Select */
.rich-text-editor .toolbar-select {
  @apply px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50 h-8;
  background: linear-gradient(to bottom, #ffffff 0%, #f8f8f8 100%);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.rich-text-editor .toolbar-select:hover {
  @apply bg-gray-50;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.rich-text-editor .toolbar-select:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Toolbar Separator */
.rich-text-editor .toolbar-separator {
  @apply w-px h-6 bg-gray-300 mx-1;
  background: linear-gradient(to bottom, transparent 0%, #d1d5db 20%, #d1d5db 80%, transparent 100%);
}

/* Color Picker */
.rich-text-editor .color-picker-group {
  @apply flex gap-1;
}

.rich-text-editor .color-btn {
  @apply relative overflow-hidden;
}

.rich-text-editor .color-input {
  @apply absolute inset-0 opacity-0 cursor-pointer;
}

/* Editor Content Styles */
.rich-text-editor .editor-content {
  @apply p-4 min-h-[200px] focus:outline-none overflow-auto;
  background: #ffffff;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.6;
}

.rich-text-editor .normal-input {
  @apply border-gray-300;
}

.rich-text-editor .error-input {
  @apply border-red-500;
}

.rich-text-editor .editor-content:focus {
  @apply ring-2 ring-blue-500 ring-opacity-50;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Placeholder */
.rich-text-editor .editor-content[data-placeholder]:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none;
  font-style: italic;
}

/* Editor Content Typography */
.rich-text-editor .editor-content h1 {
  @apply text-2xl font-bold mb-4 mt-6;
  color: #1f2937;
  border-bottom: 2px solid #e5e7eb;
  padding-bottom: 0.5rem;
}

.rich-text-editor .editor-content h1:first-child {
  @apply mt-0;
}

.rich-text-editor .editor-content h2 {
  @apply text-xl font-bold mb-3 mt-5;
  color: #374151;
}

.rich-text-editor .editor-content h2:first-child {
  @apply mt-0;
}

.rich-text-editor .editor-content h3 {
  @apply text-lg font-bold mb-2 mt-4;
  color: #4b5563;
}

.rich-text-editor .editor-content h3:first-child {
  @apply mt-0;
}

.rich-text-editor .editor-content h4 {
  @apply text-base font-bold mb-2 mt-3;
  color: #6b7280;
}

.rich-text-editor .editor-content h5 {
  @apply text-sm font-bold mb-2 mt-3;
  color: #6b7280;
}

.rich-text-editor .editor-content h6 {
  @apply text-xs font-bold mb-2 mt-3;
  color: #6b7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.rich-text-editor .editor-content p {
  @apply mb-3;
  color: #374151;
}

.rich-text-editor .editor-content p:last-child {
  @apply mb-0;
}

/* Lists */
.rich-text-editor .editor-content ul {
  @apply list-disc list-inside mb-3 pl-4;
  color: #374151;
}

.rich-text-editor .editor-content ol {
  @apply list-decimal list-inside mb-3 pl-4;
  color: #374151;
}

.rich-text-editor .editor-content li {
  @apply mb-1;
  line-height: 1.6;
}

.rich-text-editor .editor-content ul ul,
.rich-text-editor .editor-content ol ol,
.rich-text-editor .editor-content ul ol,
.rich-text-editor .editor-content ol ul {
  @apply mt-1 mb-0;
}

/* Blockquote */
.rich-text-editor .editor-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 italic mb-3 bg-blue-50 py-2;
  color: #1e40af;
  font-style: italic;
}

.rich-text-editor .editor-content blockquote p {
  @apply mb-0;
  color: inherit;
}

/* Tables */
.rich-text-editor .editor-content table {
  @apply border-collapse border border-gray-300 mb-3 w-full;
  background: #ffffff;
}

.rich-text-editor .editor-content td,
.rich-text-editor .editor-content th {
  @apply border border-gray-300 p-2;
  vertical-align: top;
}

.rich-text-editor .editor-content th {
  @apply bg-gray-100 font-bold text-left;
  color: #374151;
}

.rich-text-editor .editor-content tr:nth-child(even) {
  @apply bg-gray-50;
}

.rich-text-editor .editor-content tr:hover {
  @apply bg-blue-50;
}

/* Links */
.rich-text-editor .editor-content a {
  @apply text-blue-600 underline;
  transition: color 0.2s ease-in-out;
}

.rich-text-editor .editor-content a:hover {
  @apply text-blue-800;
  text-decoration: underline;
}

.rich-text-editor .editor-content a:visited {
  @apply text-purple-600;
}

/* Images */
.rich-text-editor .editor-content img {
  @apply max-w-full h-auto rounded-lg shadow-sm;
  margin: 1rem 0;
}

/* Code */
.rich-text-editor .editor-content code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm;
  color: #dc2626;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.rich-text-editor .editor-content pre {
  @apply bg-gray-100 p-4 rounded-lg overflow-x-auto mb-3;
  color: #374151;
}

.rich-text-editor .editor-content pre code {
  @apply bg-transparent p-0;
  color: inherit;
}

/* Disabled State */
.rich-text-editor .bg-gray-100 .editor-content {
  @apply bg-gray-100 text-gray-600;
  cursor: not-allowed;
}

.rich-text-editor .bg-gray-100 .toolbar {
  @apply bg-gray-100;
}

.rich-text-editor .bg-gray-100 .toolbar-btn,
.rich-text-editor .bg-gray-100 .toolbar-select {
  @apply opacity-60 cursor-not-allowed;
  pointer-events: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .rich-text-editor .toolbar {
    @apply text-xs gap-0.5 p-1;
  }
  
  .rich-text-editor .toolbar-btn {
    @apply min-w-[28px] h-7 px-1 text-xs;
  }
  
  .rich-text-editor .toolbar-select {
    @apply h-7 text-xs px-1;
  }
  
  .rich-text-editor .toolbar-separator {
    @apply h-5;
  }
  
  .rich-text-editor .editor-content {
    @apply p-3 text-sm;
  }
}

@media (max-width: 480px) {
  .rich-text-editor .toolbar {
    @apply flex-col items-stretch gap-1;
  }
  
  .rich-text-editor .toolbar > div {
    @apply flex items-center gap-0.5 justify-center;
  }
  
  .rich-text-editor .toolbar-separator {
    @apply hidden;
  }
}

/* Print Styles */
@media print {
  .rich-text-editor .toolbar {
    @apply hidden;
  }
  
  .rich-text-editor {
    @apply border-none shadow-none;
  }
  
  .rich-text-editor .editor-content {
    @apply p-0;
  }
}
