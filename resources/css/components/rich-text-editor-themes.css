/* Rich Text Editor Themes */

/* Default Theme (Light) */
.rich-text-editor.theme-default {
  --rte-border-color: #d1d5db;
  --rte-border-error: #ef4444;
  --rte-border-focus: #3b82f6;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #f9fafb;
  --rte-bg-disabled: #f3f4f6;
  --rte-bg-toolbar: linear-gradient(to bottom, #fafafa 0%, #f0f0f0 100%);
  --rte-bg-button: linear-gradient(to bottom, #ffffff 0%, #f8f8f8 100%);
  --rte-bg-button-active: linear-gradient(to bottom, #dbeafe 0%, #bfdbfe 100%);
  --rte-bg-button-hover: #f9fafb;
  --rte-text-primary: #374151;
  --rte-text-secondary: #6b7280;
  --rte-text-disabled: #9ca3af;
  --rte-text-placeholder: #9ca3af;
  --rte-text-active: #1e40af;
}

/* Dark Theme */
.rich-text-editor.theme-dark {
  --rte-border-color: #4b5563;
  --rte-border-error: #f87171;
  --rte-border-focus: #60a5fa;
  --rte-bg-primary: #1f2937;
  --rte-bg-secondary: #111827;
  --rte-bg-disabled: #374151;
  --rte-bg-toolbar: linear-gradient(to bottom, #374151 0%, #2d3748 100%);
  --rte-bg-button: linear-gradient(to bottom, #4b5563 0%, #374151 100%);
  --rte-bg-button-active: linear-gradient(to bottom, #1e3a8a 0%, #1e40af 100%);
  --rte-bg-button-hover: #4b5563;
  --rte-text-primary: #f9fafb;
  --rte-text-secondary: #d1d5db;
  --rte-text-disabled: #6b7280;
  --rte-text-placeholder: #6b7280;
  --rte-text-active: #93c5fd;
}

/* Minimal Theme */
.rich-text-editor.theme-minimal {
  --rte-border-color: #e5e7eb;
  --rte-border-error: #fca5a5;
  --rte-border-focus: #93c5fd;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #ffffff;
  --rte-bg-disabled: #f9fafb;
  --rte-bg-toolbar: #ffffff;
  --rte-bg-button: #ffffff;
  --rte-bg-button-active: #eff6ff;
  --rte-bg-button-hover: #f3f4f6;
  --rte-text-primary: #111827;
  --rte-text-secondary: #6b7280;
  --rte-text-disabled: #9ca3af;
  --rte-text-placeholder: #9ca3af;
  --rte-text-active: #2563eb;
  --rte-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --rte-shadow-md: 0 1px 3px rgba(0, 0, 0, 0.1);
  --rte-shadow-focus: 0 0 0 3px rgba(147, 197, 253, 0.1);
}

/* Colorful Theme */
.rich-text-editor.theme-colorful {
  --rte-border-color: #d1d5db;
  --rte-border-error: #f87171;
  --rte-border-focus: #8b5cf6;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #fef3c7;
  --rte-bg-disabled: #f3f4f6;
  --rte-bg-toolbar: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --rte-bg-button: linear-gradient(135deg, #ffffff 0%, #f0f9ff 100%);
  --rte-bg-button-active: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
  --rte-bg-button-hover: #f0f9ff;
  --rte-text-primary: #1f2937;
  --rte-text-secondary: #6b7280;
  --rte-text-disabled: #9ca3af;
  --rte-text-placeholder: #9ca3af;
  --rte-text-active: #7c3aed;
}

.rich-text-editor.theme-colorful .toolbar-btn {
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
}

.rich-text-editor.theme-colorful .toolbar-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.rich-text-editor.theme-colorful .toolbar-btn.active {
  background: var(--rte-bg-button-active);
  color: var(--rte-text-active);
}

.rich-text-editor.theme-colorful .toolbar-select {
  color: #ffffff;
  border-color: rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

/* Professional Theme */
.rich-text-editor.theme-professional {
  --rte-border-color: #cbd5e1;
  --rte-border-error: #ef4444;
  --rte-border-focus: #0ea5e9;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #f8fafc;
  --rte-bg-disabled: #f1f5f9;
  --rte-bg-toolbar: linear-gradient(to bottom, #f8fafc 0%, #e2e8f0 100%);
  --rte-bg-button: linear-gradient(to bottom, #ffffff 0%, #f1f5f9 100%);
  --rte-bg-button-active: linear-gradient(to bottom, #e0f2fe 0%, #bae6fd 100%);
  --rte-bg-button-hover: #f8fafc;
  --rte-text-primary: #0f172a;
  --rte-text-secondary: #475569;
  --rte-text-disabled: #94a3b8;
  --rte-text-placeholder: #94a3b8;
  --rte-text-active: #0369a1;
  --rte-border-radius: 0.375rem;
  --rte-button-radius: 0.25rem;
}

/* Soft Theme */
.rich-text-editor.theme-soft {
  --rte-border-color: #e5e7eb;
  --rte-border-error: #fca5a5;
  --rte-border-focus: #f472b6;
  --rte-bg-primary: #fefefe;
  --rte-bg-secondary: #fdf2f8;
  --rte-bg-disabled: #faf5ff;
  --rte-bg-toolbar: linear-gradient(to bottom, #fdf2f8 0%, #fce7f3 100%);
  --rte-bg-button: linear-gradient(to bottom, #ffffff 0%, #fdf2f8 100%);
  --rte-bg-button-active: linear-gradient(to bottom, #fce7f3 0%, #fbcfe8 100%);
  --rte-bg-button-hover: #fdf2f8;
  --rte-text-primary: #374151;
  --rte-text-secondary: #6b7280;
  --rte-text-disabled: #9ca3af;
  --rte-text-placeholder: #9ca3af;
  --rte-text-active: #be185d;
  --rte-border-radius: 1rem;
  --rte-button-radius: 0.75rem;
}

/* High Contrast Theme */
.rich-text-editor.theme-high-contrast {
  --rte-border-color: #000000;
  --rte-border-error: #dc2626;
  --rte-border-focus: #1d4ed8;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #f9fafb;
  --rte-bg-disabled: #e5e7eb;
  --rte-bg-toolbar: #f3f4f6;
  --rte-bg-button: #ffffff;
  --rte-bg-button-active: #dbeafe;
  --rte-bg-button-hover: #f9fafb;
  --rte-text-primary: #000000;
  --rte-text-secondary: #374151;
  --rte-text-disabled: #6b7280;
  --rte-text-placeholder: #6b7280;
  --rte-text-active: #1d4ed8;
  --rte-shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.2);
  --rte-shadow-md: 0 4px 6px rgba(0, 0, 0, 0.3);
  --rte-shadow-focus: 0 0 0 3px rgba(29, 78, 216, 0.3);
}

.rich-text-editor.theme-high-contrast .toolbar-btn {
  border-width: 2px;
  font-weight: 600;
}

.rich-text-editor.theme-high-contrast .toolbar-select {
  border-width: 2px;
  font-weight: 600;
}

/* Compact Theme */
.rich-text-editor.theme-compact {
  --rte-toolbar-padding: 0.25rem;
  --rte-content-padding: 0.75rem;
  --rte-button-padding: 0.25rem;
}

.rich-text-editor.theme-compact .toolbar-btn {
  min-width: 24px;
  height: 24px;
  font-size: 0.75rem;
}

.rich-text-editor.theme-compact .toolbar-select {
  height: 24px;
  font-size: 0.75rem;
  padding: 0.125rem 0.25rem;
}

.rich-text-editor.theme-compact .toolbar-separator {
  height: 16px;
}

/* Large Theme */
.rich-text-editor.theme-large {
  --rte-toolbar-padding: 0.75rem;
  --rte-content-padding: 1.5rem;
  --rte-button-padding: 0.75rem;
}

.rich-text-editor.theme-large .toolbar-btn {
  min-width: 40px;
  height: 40px;
  font-size: 1rem;
}

.rich-text-editor.theme-large .toolbar-select {
  height: 40px;
  font-size: 1rem;
  padding: 0.5rem 0.75rem;
}

.rich-text-editor.theme-large .toolbar-separator {
  height: 32px;
}

/* Custom Brand Theme */
.rich-text-editor.theme-brand {
  --rte-border-color: #d1d5db;
  --rte-border-error: #ef4444;
  --rte-border-focus: #465fff;
  --rte-bg-primary: #ffffff;
  --rte-bg-secondary: #f2f7ff;
  --rte-bg-disabled: #f3f4f6;
  --rte-bg-toolbar: linear-gradient(to bottom, #f2f7ff 0%, #ecf3ff 100%);
  --rte-bg-button: linear-gradient(to bottom, #ffffff 0%, #f2f7ff 100%);
  --rte-bg-button-active: linear-gradient(to bottom, #dde9ff 0%, #c2d6ff 100%);
  --rte-bg-button-hover: #f2f7ff;
  --rte-text-primary: #101828;
  --rte-text-secondary: #475467;
  --rte-text-disabled: #98a2b3;
  --rte-text-placeholder: #98a2b3;
  --rte-text-active: #465fff;
}
