# CkEditor Component Usage

The CkEditor component is a rich text editor wrapper around CKEditor 5 with features similar to other form components in the project.

## Features

- **Label support** - Display field labels with optional required indicator
- **Error handling** - Show validation errors with consistent styling
- **Disabled state** - Disable the editor with gray background
- **Placeholder text** - Show placeholder text when editor is empty
- **Consistent styling** - Matches other form components (VInput, VTextarea, etc.)
- **Auto-generated IDs** - Unique IDs for accessibility
- **v-model support** - Two-way data binding

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | String | `''` | The HTML content of the editor |
| `label` | String | - | Label text to display above the editor |
| `error` | String | - | Error message to display below the editor |
| `disabled` | Boolean | `false` | Whether the editor is disabled |
| `required` | Boolean | `false` | Whether to show required indicator (*) |
| `placeholder` | String | `''` | Placeholder text for empty editor |
| `id` | String | auto-generated | Unique ID for the editor |

## Basic Usage

```vue
<template>
  <CkEditor
    v-model="content"
    label="Article Content"
    placeholder="Enter your content here..."
  />
</template>

<script setup>
import { ref } from 'vue'
import CkEditor from '@/components/common/shared/CkEditor.vue'

const content = ref('')
</script>
```

## With Validation

```vue
<template>
  <CkEditor
    v-model="form.description"
    :error="form.errors.description"
    label="Description"
    placeholder="Enter description..."
    required
  />
</template>

<script setup>
import { useForm } from '@inertiajs/vue3'
import CkEditor from '@/components/common/shared/CkEditor.vue'

const form = useForm({
  description: ''
})
</script>
```

## Disabled State

```vue
<template>
  <CkEditor
    v-model="content"
    label="Read-only Content"
    :disabled="true"
  />
</template>
```

## With useValidation Composable

```vue
<template>
  <form @submit.prevent="handleSubmit">
    <CkEditor
      v-model="form.content"
      :error="form.errors.content"
      label="Content"
      placeholder="Enter content..."
      required
      :disabled="isPreview"
    />
  </form>
</template>

<script setup>
import { useValidation } from '@/composables/useValidation'
import CkEditor from '@/components/common/shared/CkEditor.vue'

const { form, validateBeforePreview } = useValidation({
  content: ''
})

const handleSubmit = () => {
  if (validateBeforePreview({ content: ['required'] })) {
    // Submit form
  }
}
</script>
```

## Styling

The component automatically applies consistent styling:

- **Normal state**: Gray border with blue focus ring
- **Error state**: Red border with red focus ring  
- **Disabled state**: Gray background for both toolbar and editor
- **Responsive**: Works on all screen sizes

## Toolbar Features

The editor includes a comprehensive toolbar with:

- Headings
- Bold/Italic formatting
- Links
- Bulleted/Numbered lists
- Indentation
- Block quotes
- Tables
- Undo/Redo

## Notes

- The component uses CKEditor 5 Decoupled Document Build
- Toolbar is automatically attached above the editor content
- Content is automatically synced with v-model
- File uploads are disabled by default (can be enabled if needed)
- The editor has a minimum height of 150px for the content area
