# Rich Text Editor Component

M<PERSON>t component Rich Text Editor tự build với đầy đủ chức năng tương tự CKEditor, đ<PERSON><PERSON><PERSON> phát triển bằng Vue 3 và TypeScript.

## Tính năng

### Định dạng văn bản
- **Bold, Italic, Underline, Strikethrough**: <PERSON><PERSON><PERSON> định dạng văn bản cơ bản
- **Superscript, Subscript**: Chỉ số trên và chỉ số dưới
- **Font Family**: Lựa chọn font chữ (Arial, Times New Roman, Courier New, Helvetica, Georgia)
- **Font Size**: Thay đổi kích thước chữ (8px - 36px)
- **Text Color**: Thay đổi màu chữ
- **Background Color**: Thay đổi màu nền của text

### Căn chỉnh văn bản
- **Left Align**: Căn trái
- **Center Align**: Căn gi<PERSON>a
- **Right Align**: <PERSON><PERSON><PERSON> phải
- **Justify**: <PERSON><PERSON><PERSON> đều

### <PERSON>h sách và thụt lề
- **Bullet List**: <PERSON><PERSON> sách không thứ tự
- **Numbered List**: Danh sách có thứ tự
- **Indent/Outdent**: Tăng/giảm thụt lề

### Chèn nội dung
- **Links**: Chèn liên kết
- **Images**: Chèn hình ảnh
- **Tables**: Chèn bảng

### Tính năng khác
- **Undo/Redo**: Hoàn tác và làm lại
- **Fullscreen Mode**: Chế độ toàn màn hình
- **Responsive Design**: Tương thích với mobile
- **Error State**: Hiển thị trạng thái lỗi
- **Disabled State**: Trạng thái vô hiệu hóa

## Cách sử dụng

### Import component

```vue
<script setup lang="ts">
import RichTextEditor from '@/components/common/shared/RichTextEditor.vue';
import { ref } from 'vue';

const content = ref('<p>Nội dung mẫu</p>');
</script>
```

### Sử dụng cơ bản

```vue
<template>
  <RichTextEditor
    v-model="content"
    label="Nội dung bài viết"
    placeholder="Nhập nội dung..."
  />
</template>
```

### Sử dụng với validation

```vue
<template>
  <RichTextEditor
    v-model="content"
    label="Nội dung bài viết"
    placeholder="Nhập nội dung..."
    :error="errors.content"
    required
  />
</template>
```

### Sử dụng với custom height

```vue
<template>
  <RichTextEditor
    v-model="content"
    label="Mô tả ngắn"
    height="200px"
  />
</template>
```

### Sử dụng disabled state

```vue
<template>
  <RichTextEditor
    v-model="content"
    label="Nội dung (chỉ đọc)"
    disabled
  />
</template>
```

## Props

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `modelValue` | `string` | `''` | Nội dung HTML của editor |
| `label` | `string` | `undefined` | Label hiển thị phía trên editor |
| `placeholder` | `string` | `'Nhập nội dung...'` | Placeholder text |
| `error` | `string` | `undefined` | Thông báo lỗi |
| `disabled` | `boolean` | `false` | Vô hiệu hóa editor |
| `required` | `boolean` | `false` | Hiển thị dấu * bắt buộc |
| `height` | `string` | `'300px'` | Chiều cao của editor |
| `id` | `string` | `auto-generated` | ID của editor |

## Events

| Event | Payload | Description |
|-------|---------|-------------|
| `update:modelValue` | `string` | Được emit khi nội dung thay đổi |

## Styling

Component sử dụng Tailwind CSS và có thể được customize thông qua CSS classes:

```vue
<template>
  <RichTextEditor
    v-model="content"
    class="my-custom-editor"
  />
</template>

<style>
.my-custom-editor {
  /* Custom styles */
}
</style>
```

## Composable useRichTextEditor

Component sử dụng composable `useRichTextEditor` để xử lý logic. Bạn có thể sử dụng composable này để tạo editor tùy chỉnh:

```typescript
import { useRichTextEditor } from '@/composables/useRichTextEditor';

const editorRef = ref<HTMLElement>();
const {
  execCommand,
  queryCommandState,
  insertHTML,
  undo,
  redo,
  canUndo,
  canRedo
} = useRichTextEditor(editorRef);
```

## Ví dụ hoàn chỉnh

```vue
<script setup lang="ts">
import { ref } from 'vue';
import RichTextEditor from '@/components/common/shared/RichTextEditor.vue';

const form = ref({
  title: '',
  content: '<p>Nội dung mẫu</p>',
  description: ''
});

const errors = ref({});

const handleSubmit = () => {
  // Xử lý submit form
  console.log('Form data:', form.value);
};
</script>

<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <div>
      <label class="block text-sm font-medium text-gray-700 mb-2">
        Tiêu đề
      </label>
      <input
        v-model="form.title"
        type="text"
        class="w-full border border-gray-300 rounded-lg px-3 py-2"
        placeholder="Nhập tiêu đề..."
      />
    </div>

    <RichTextEditor
      v-model="form.content"
      label="Nội dung chính"
      :error="errors.content"
      required
      height="400px"
    />

    <RichTextEditor
      v-model="form.description"
      label="Mô tả ngắn"
      height="150px"
    />

    <button
      type="submit"
      class="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
    >
      Lưu
    </button>
  </form>
</template>
```

## Lưu ý

1. **HTML Output**: Component trả về HTML content, cần validate và sanitize trước khi lưu vào database
2. **Security**: Tránh render HTML trực tiếp từ user input mà không sanitize
3. **Performance**: Với nội dung lớn, nên debounce việc update modelValue
4. **Browser Support**: Sử dụng document.execCommand, có thể không hoạt động trên một số browser cũ

## Troubleshooting

### Lỗi import composable
Đảm bảo đường dẫn import đúng:
```typescript
import { useRichTextEditor } from '../../../composables/useRichTextEditor';
```

### Styling không hiển thị đúng
Đảm bảo Tailwind CSS được cấu hình đúng và các class được include trong build.

### Fullscreen không hoạt động
Kiểm tra z-index và position CSS của các element cha.
